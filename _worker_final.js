// � 高性能版本 v5.0 - 性能优化和简化
// 优化内容:
// 1. 简化 TCP 读取循环，移除过度的条件检查
// 2. 优化 releaseLock 逻辑，移除复杂的状态检查
// 3. 移除延迟流取消机制，立即释放资源
// 4. 简化 WebSocket 事件处理器，减少包装层次
// 5. 优化发送函数，移除不必要的 ULTIMATE_SAFE 包装
// 6. 显示所有错误，移除静默处理机制
// 🛡️ Worker 级别全局异常处理 - 这是最后的防线！
const GLOBAL_ERROR_HANDLER = {
    errors: [],
    maxErrors: 100,
    
    log(error, context = 'Unknown', stack = null) {
        // 🔧 增强的实时错误处理 - 确保错误被正确记录
        const errorInfo = {
            timestamp: new Date().toISOString(),
            context,
            message: error?.message || String(error),
            stack: stack || error?.stack || 'No stack trace',
            type: error?.constructor?.name || 'Unknown',
            errorId: Math.random().toString(36).substr(2, 9) // 添加唯一ID便于追踪
        };

        this.errors.push(errorInfo);
        if (this.errors.length > this.maxErrors) {
            this.errors.shift(); // 保持错误日志在合理范围内
        }

        // 🔧 增强日志输出，包含错误ID和当前错误总数
        console.error(`[FINAL GUARD] ${context} [ID:${errorInfo.errorId}] [Total:${this.errors.length}]:`, errorInfo);

        // 🔧 立即输出到控制台确保实时性
        console.log(`[ERROR LOGGED] ${errorInfo.timestamp} - ${context}: ${errorInfo.message}`);

        return errorInfo;
    },
    
    getRecentErrors() {
        return this.errors.slice(-10); // 返回最近10个错误
    },

    clear() {
        this.errors = [];
        console.log('[GLOBAL_ERROR_HANDLER] Error history cleared');
    },

    filterExistingErrors() {
        // 不再过滤任何错误，保留所有错误记录
        console.log(`[GLOBAL_ERROR_HANDLER] Keeping all ${this.errors.length} errors (no filtering)`);
        return 0; // 没有过滤任何错误
    }
};

// 🛡️ 尝试设置全局异常处理器（如果 Worker 环境支持）
try {
    if (typeof globalThis !== 'undefined') {
        // 🔧 强化的未处理Promise拒绝处理器
        globalThis.addEventListener?.('unhandledrejection', (event) => {
            const error = event.reason;

            // 🔧 所有未处理的拒绝都应该被记录为真正的错误
            GLOBAL_ERROR_HANDLER.log(error, 'UnhandledPromiseRejection');

            event.preventDefault(); // 防止异常传播
        });
        
        // 尝试捕获未处理的异常
        globalThis.addEventListener?.('error', (event) => {
            GLOBAL_ERROR_HANDLER.log(event.error, 'UnhandledException', event.error?.stack);
            event.preventDefault(); // 防止异常传播
        });
    }
} catch (globalSetupError) {
    console.warn('Global error handler setup failed:', globalSetupError);
}

// 🛡️ 终极安全包装器 - 比之前更强
const ULTIMATE_SAFE = {
    async(asyncFn, context = 'AsyncOp', fallback = null) {
        return new Promise((resolve) => {
            try {
                const result = asyncFn();
                if (result && typeof result.then === 'function') {
                    result
                        .then(resolve)
                        .catch(error => {
                            GLOBAL_ERROR_HANDLER.log(error, context);
                            resolve(fallback);
                        });
                } else {
                    resolve(result);
                }
            } catch (error) {
                GLOBAL_ERROR_HANDLER.log(error, context);
                resolve(fallback);
            }
        });
    },
    
    sync(syncFn, context = 'SyncOp', fallback = null) {
        try {
            return syncFn();
        } catch (error) {
            GLOBAL_ERROR_HANDLER.log(error, context);
            return fallback;
        }
    },
    
    timeout(callback, delay, context = 'Timeout') {
        return setTimeout(() => {
            this.sync(() => callback(), context);
        }, delay);
    },
    
    // 🛡️ 特殊的 Promise 包装器 - 终极强化版本，彻底防止 Promise 拒绝泄漏
    promise(promiseFn, context = 'Promise', fallback = null) {
        return new Promise((resolve, reject) => {
            try {
                const promise = promiseFn();
                if (promise && typeof promise.then === 'function') {
                    // 🔧 创建一个完全隔离的 Promise 链
                    const isolatedPromise = Promise.resolve(promise);

                    isolatedPromise
                        .then(result => {
                            resolve(result);
                        })
                        .catch(error => {
                            // 🔧 记录错误并解析为 fallback
                            GLOBAL_ERROR_HANDLER.log(error, context);
                            resolve(fallback);
                        });

                    // 🔧 确保原始 promise 的拒绝也被捕获
                    promise.catch(error => {
                        // 静默处理，防止泄漏
                        // 主要的错误处理在上面的 isolatedPromise.catch 中
                    });

                } else {
                    resolve(promise);
                }
            } catch (error) {
                GLOBAL_ERROR_HANDLER.log(error, context);
                resolve(fallback);
            }
        });
    }
};

import { connect } from 'cloudflare:sockets';

const globalControllerConfig = {
    connectMode: 'direct',
    retryMode: 'relayip',
    targetProtocolType0: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d8-_-1b0-_-194-_-1cc-_-1cc',
    targetProtocolType1: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d0-_-1c8-_-1bc-_-1a8-_-184-_-1b8',
    targetPathType0: 'vlws',
    targetPathType1: 'trws',
};

const globalSessionConfig = {
    connect: {
        connectMode: 'direct',
        retryMode: 'relayip',
    },
    user: {
        id: '49f37b98-c37f-4d46-be93-1fe0a742dd43',
        pass: 'a233255z',
        sha224: '419023a775279d21cdbda41971c0bb52e962f11b4f4bfba6015a268b',
    },
    relay: {
        ip: 'jp.almain126.changeip.biz',
        _port: null,
        get port() {
            if (this._port !== null) {
                return this._port;
            }
            return this.ip.includes(':') ? this.ip.split(':')[1] : (null || undefined);
        },
        set port(value) {
            this._port = value;
        },
        socks: 'web5.serv00.com:13668',
    },
    api: {
        addresses: 'https://rtmainalraw.pages.dev/az/index.txt',
        addresses2: 'https://rtmainalraw.pages.dev/az/main.txt',
        directTemplate: 'https://rtmainalraw.pages.dev/az/templatedirect.txt',
        globalTemplate: 'https://rtmainalraw.pages.dev/az/templateglobal.txt',
    },
    misc: {
        subName: 'myMain',
    }
};

const WS_STATES = {
    CONNECTING: 0,
    OPEN: 1,
    CLOSING: 2,
    CLOSED: 3
};

// 🔧 WebSocket 消息大小限制常量
const WS_MESSAGE_LIMITS = {
    MAX_SIZE: 1048576,        // 1 MiB - Cloudflare Workers 限制
    CHUNK_SIZE: 524288,       // 512 KB - 安全的分块大小
    SAFE_MARGIN: 1024         // 1 KB - 安全边界
};

// 🔧 获取数据的字节大小
const getDataSize = (data) => {
    if (typeof data === 'string') {
        return new TextEncoder().encode(data).length;
    } else if (data instanceof ArrayBuffer) {
        return data.byteLength;
    } else if (data instanceof Uint8Array || data instanceof DataView) {
        return data.byteLength;
    } else if (data && typeof data.byteLength === 'number') {
        return data.byteLength;
    }
    return 0;
};

export default {
    async fetch(request, env, ctx) {
        // 🛡️ 最外层防护 - 绝对不让任何异常逃脱
        return ULTIMATE_SAFE.promise(async () => {
            const { CONNECT_MODE, RETRY_MODE, USER_GUID, USER_PASS, USER_SHA224, RELAY_IP, RELAY_SOCKS, API_TXT, API_TXT_2, API_DIRECT_TEMPLATE_URL, API_GLOBAL_TEMPLATE_URL } = env;
            
            globalControllerConfig.connectMode = (CONNECT_MODE || globalSessionConfig.connect.connectMode).toLowerCase();
            globalControllerConfig.retryMode = (RETRY_MODE || globalSessionConfig.connect.retryMode).toLowerCase();

            globalSessionConfig.user.id = USER_GUID || globalSessionConfig.user.id;
            globalSessionConfig.user.pass = USER_PASS || globalSessionConfig.user.pass;
            globalSessionConfig.user.sha224 = USER_SHA224 || globalSessionConfig.user.sha224;
            globalSessionConfig.relay.ip = RELAY_IP || globalSessionConfig.relay.ip;
            globalSessionConfig.relay.socks = RELAY_SOCKS || globalSessionConfig.relay.socks;
            globalSessionConfig.api.addresses = API_TXT || globalSessionConfig.api.addresses;
            globalSessionConfig.api.addresses2 = API_TXT_2 || globalSessionConfig.api.addresses2;
            globalSessionConfig.api.directTemplate = API_DIRECT_TEMPLATE_URL || globalSessionConfig.api.directTemplate;
            globalSessionConfig.api.globalTemplate = API_GLOBAL_TEMPLATE_URL || globalSessionConfig.api.globalTemplate;

            const userAgent = (request.headers.get('User-Agent') || 'null').toLowerCase();
            const url = new URL(request.url);
            const upgradeHeader = request.headers.get('Upgrade');
            
            if (!upgradeHeader || upgradeHeader !== 'websocket') {
                switch (url.pathname) {
                    case '/':
                        return new Response(null, { status: 204 });
                    case '/debug-errors':
                        // � 实时调试端点 - 确保显示最新错误，无缓存
                        const currentTime = new Date().toISOString();
                        const debugInfo = {
                            allErrors: [...GLOBAL_ERROR_HANDLER.errors], // 创建副本确保实时性
                            totalErrors: GLOBAL_ERROR_HANDLER.errors.length,
                            timestamp: currentTime,
                            lastErrorTime: GLOBAL_ERROR_HANDLER.errors.length > 0 ?
                                GLOBAL_ERROR_HANDLER.errors[GLOBAL_ERROR_HANDLER.errors.length - 1].timestamp : 'No errors',
                            config: {
                                connectMode: globalControllerConfig.connectMode,
                                retryMode: globalControllerConfig.retryMode,
                                relayIp: globalSessionConfig.relay.ip,
                                relayPort: globalSessionConfig.relay.port
                            },
                            errorStats: {
                                streamCancelled: GLOBAL_ERROR_HANDLER.errors.filter(e =>
                                    e.message?.includes('cancelled') || e.type === 'AbortError'
                                ).length,
                                unhandledPromiseRejection: GLOBAL_ERROR_HANDLER.errors.filter(e =>
                                    e.context === 'UnhandledPromiseRejection'
                                ).length,
                                connectionFailed: GLOBAL_ERROR_HANDLER.errors.filter(e =>
                                    e.message?.includes('Connection failed') || e.message?.includes('dial')
                                ).length,
                                protocolErrors: GLOBAL_ERROR_HANDLER.errors.filter(e =>
                                    e.message?.includes('Protocol') || e.message?.includes('Header')
                                ).length,
                                tcpForwardErrors: GLOBAL_ERROR_HANDLER.errors.filter(e =>
                                    e.context === 'TCPForward'
                                ).length,
                                tcpReadErrors: GLOBAL_ERROR_HANDLER.errors.filter(e =>
                                    e.context === 'TCPRead'
                                ).length,
                                webSocketSendErrors: GLOBAL_ERROR_HANDLER.errors.filter(e =>
                                    e.context === 'WebSocketSend' || e.context === 'WebSocketChunkedSend'
                                ).length,
                                webSocketMessageTooLarge: GLOBAL_ERROR_HANDLER.errors.filter(e =>
                                    e.context === 'WebSocketMessageTooLarge'
                                ).length
                            },
                            debug: {
                                errorHandlerStatus: 'active',
                                maxErrors: GLOBAL_ERROR_HANDLER.maxErrors,
                                currentErrorCount: GLOBAL_ERROR_HANDLER.errors.length
                            }
                        };

                        return new Response(JSON.stringify(debugInfo, null, 2), {
                            headers: {
                                'Content-Type': 'application/json',
                                'Cache-Control': 'no-cache, no-store, must-revalidate',
                                'Pragma': 'no-cache',
                                'Expires': '0'
                            }
                        });

                    case `/a`:
                        return new Response(null, { status: 204 });
                    case `/z`:
                        return new Response(null, { status: 204 });
                    case `/aazz`:
                        return ULTIMATE_SAFE.promise(async () => {
                            const inputString = await fetchRemoteData(globalSessionConfig.api.addresses);
                            const inputTemplate = await fetchRemoteData(globalSessionConfig.api.globalTemplate);

                            const getConfig = (type, tls) =>
                                url.searchParams.toString().includes('b64') || url.searchParams.toString().includes('base64')
                                    ? (() => {
                                        const configs = generateConfigs(inputString, globalSessionConfig.user.id, globalSessionConfig.user.pass, tls, request.headers.get('Host'));
                                        if (type === 'both') return btoa(configs.protocolP0 + '\n' + configs.protocolP1);
                                        if (type === 'p0') return btoa(configs.protocolP0);
                                        if (type === 'p1') return btoa(configs.protocolP1);
                                    })()
                                    : getCustomConfig(inputString, globalSessionConfig.user.id, globalSessionConfig.user.pass, tls, request.headers.get('Host'), type, inputTemplate);

                            const configs = {
                                both: getConfig('both', true),
                                p0: getConfig('p0', true),
                                p1: getConfig('p1', true),
                                bothNotls: getConfig('both', false),
                                p0Notls: getConfig('p0', false),
                                p1Notls: getConfig('p1', false)
                            };

                            const configMappings = [
                                { param: 'both', config: configs.both },
                                { param: 'az', config: configs.p0 },
                                { param: 'za', config: configs.p1 },
                                { param: 'both-notls', config: configs.bothNotls },
                                { param: 'az-notls', config: configs.p0Notls },
                                { param: 'za-notls', config: configs.p1Notls }
                            ];

                            const getResponseConfig = (isMozilla) => ({
                                status: 200,
                                headers: {
                                    "Content-Type": "text/plain;charset=utf-8",
                                    ...(isMozilla ? {} : {
                                        "Content-Disposition": `attachment; filename=${globalSessionConfig.misc.subName}; filename*=utf-8''${encodeURIComponent(globalSessionConfig.misc.subName)}`,
                                        "Profile-Update-Interval": "6"
                                    })
                                }
                            });

                            const isMozilla = userAgent && userAgent.includes('mozilla');
                            const prefixes = ['b64', 'base64'];

                            for (const prefix of prefixes) {
                                for (const { param, config } of configMappings) {
                                    const fullParam = `${prefix}${param}`;
                                    if (url.searchParams.has(fullParam)) {
                                        return new Response(config, getResponseConfig(isMozilla));
                                    }
                                }
                            }

                            for (const { param, config } of configMappings) {
                                if (url.searchParams.has(param)) {
                                    return new Response(config, getResponseConfig(isMozilla));
                                }
                            }

                            const isB64Request = url.searchParams.toString().includes('b64') || url.searchParams.toString().includes('base64');
                            const defaultConfig = isB64Request
                                ? getConfig('both', true)
                                : configMappings[0].config;

                            return new Response(defaultConfig, getResponseConfig(isMozilla));

                        }, 'ConfigGeneration', new Response('Config generation failed', { status: 500 }));

                    default:
                        return new Response('Not found', { status: 404 });
                }
            } else if (upgradeHeader === 'websocket') {
                if (url.searchParams.has('relayip')) {
                    globalSessionConfig.relay.ip = url.searchParams.get('relayip') || globalSessionConfig.relay.ip.trim();
                    globalControllerConfig.retryMode = ('relayip').toLowerCase();
                } else if (url.pathname.toLowerCase().includes('/relayip=')) {
                    globalSessionConfig.relay.ip = url.pathname.split('/relayip=')[1]?.trim() || globalSessionConfig.relay.ip.trim();
                    globalControllerConfig.retryMode = ('relayip').toLowerCase();
                } else if (url.searchParams.has('socks')) {
                    globalSessionConfig.relay.socks = url.searchParams.get('socks') || globalSessionConfig.relay.socks.trim();
                    globalControllerConfig.retryMode = ('relaysocks').toLowerCase();
                } else if (url.pathname.toLowerCase().includes('/socks=')) {
                    globalSessionConfig.relay.socks = url.pathname.split('/socks=')[1]?.trim() || globalSessionConfig.relay.socks.trim();
                    globalControllerConfig.retryMode = ('relaysocks').toLowerCase();
                }

                const [relayIp, relayPort] = globalSessionConfig.relay.ip.split(':');
                globalSessionConfig.relay.ip = relayIp;
                if (relayPort) globalSessionConfig.relay.port = relayPort;

                const HANDLER_CHOICE = 1;
                const handlerConfigs = {
                    1: { sessionA: handleSession, sessionZ: handleSession },
                };
                const config = handlerConfigs[HANDLER_CHOICE];
                const handleSessionA = (request, env, ctx) => config.sessionA(request, env, ctx, globalControllerConfig.targetProtocolType0);
                const handleSessionZ = (request, env, ctx) => config.sessionZ(request, env, ctx, globalControllerConfig.targetProtocolType1);

                let handler;
                const pathType = url.pathname.split('/')[1];
                switch (pathType) {
                    case globalControllerConfig.targetPathType1:
                        handler = handleSessionZ;
                        break;
                    case globalControllerConfig.targetPathType0:
                        handler = handleSessionA;
                        break;
                    default:
                        handler = handleSessionA;
                }

                return await handler(request, env, ctx);
            }
        }, 'MainFetch', new Response(`Server Error - Check /debug-errors for details`, { status: 500 }));
    },
};

// 🛡️ 终极终极版本的 handleSession 函数
export async function handleSession(request, env, ctx, protocolMode) {
    // 🔧 额外的 Promise 拒绝捕获层 - 确保没有任何拒绝泄漏
    return ULTIMATE_SAFE.promise(async () => {
        let client = null;
        let server = null;
        let tcpInterface = null;
        let tcpReader = null;
        let tcpWriter = null;
        let upstreamReadable = null;
        let pipePromise = null;
        let isSessionClosed = false;
        let cleanupInProgress = false;
        let forceStop = false;
        let streamCancelled = false; // 🔧 添加流取消状态跟踪

        // 🛡️ 绝对安全的清理函数
        const absoluteCleanup = (reason = 'Unknown') => {
            return ULTIMATE_SAFE.sync(() => {
                if (isSessionClosed || cleanupInProgress) return;
                cleanupInProgress = true;
                isSessionClosed = true;

                console.log(`[ABSOLUTE CLEANUP] ${reason}`);

                // 首先设置强制停止标志，中断所有异步操作
                forceStop = true;

                // 🔧 移除 abortController.abort() 调用以改善性能
                // TCP读取循环会通过检查 forceStop 标志优雅退出

                // 立即清理大部分资源，无延迟
                // 🚀 简化的资源清理逻辑
                if (tcpReader) {
                    try {
                        tcpReader.releaseLock();
                        console.log('[Cleanup] tcpReader released');
                    } catch (e) {
                        // 静默处理释放错误，避免日志污染
                    }
                    tcpReader = null;
                }

                if (tcpWriter) {
                    try {
                        tcpWriter.releaseLock();
                        console.log('[Cleanup] tcpWriter released');
                    } catch (e) {
                        // 静默处理释放错误，避免日志污染
                    }
                    tcpWriter = null;
                }

                ULTIMATE_SAFE.sync(() => {
                    if (tcpInterface) {
                        ULTIMATE_SAFE.sync(() => {
                            if (typeof tcpInterface.close === 'function') {
                                tcpInterface.close();
                            }
                        }, 'tcpInterface.close');
                        tcpInterface = null;
                    }
                }, 'cleanup.tcpInterface');

                ULTIMATE_SAFE.sync(() => {
                    if (server) {
                        ULTIMATE_SAFE.sync(() => {
                            if (server.readyState === WS_STATES.OPEN || server.readyState === WS_STATES.CONNECTING) {
                                server.close(1013, reason);
                            }
                        }, 'server.close');
                    }
                }, 'cleanup.server');

                // 立即中止管道操作
                if (pipePromise) {
                    ULTIMATE_SAFE.async(() => {
                        try {
                            // 尝试中止管道操作
                            if (pipePromise && typeof pipePromise.catch === 'function') {
                                pipePromise.catch(() => {}); // 忽略管道错误
                            }
                        } catch (e) {
                            console.warn('Pipe promise abort failed:', e.message);
                        }
                    }, 'cleanup.pipePromise');
                }

                // � 移除手动流取消 - 流会自动关闭，避免"locked to a reader"错误
                // ReadableStream会在pipeTo操作结束时自动关闭，无需手动取消
                if (upstreamReadable && !streamCancelled) {
                    streamCancelled = true; // 🔧 标记流状态，避免重复操作
                    console.log('Upstream readable stream will auto-close');
                }

                cleanupInProgress = false;
            }, 'AbsoluteCleanup');
        };

        // 🔧 改进的发送函数 - 支持大消息分块发送
        const absoluteSend = (data) => {
            try {
                if (!server || server.readyState !== WS_STATES.OPEN || isSessionClosed) {
                    return false;
                }

                const dataSize = getDataSize(data);

                // 🔧 检查消息大小，如果超过安全限制则分块发送
                if (dataSize > WS_MESSAGE_LIMITS.CHUNK_SIZE) {
                    console.log(`[WebSocket] Large message detected (${dataSize} bytes), splitting into chunks`);
                    return sendInChunks(data, dataSize);
                } else {
                    // 正常发送小消息
                    server.send(data);
                    return true;
                }
            } catch (e) {
                console.warn('[WebSocket] Send failed:', e.message);
                GLOBAL_ERROR_HANDLER.log(e, 'WebSocketSend');
                return false;
            }
        };

        // 🔧 分块发送大消息
        const sendInChunks = (data, totalSize) => {
            try {
                const chunkSize = WS_MESSAGE_LIMITS.CHUNK_SIZE;
                let offset = 0;
                let chunkCount = 0;

                while (offset < totalSize) {
                    if (server.readyState !== WS_STATES.OPEN || isSessionClosed) {
                        console.warn('[WebSocket] Connection closed during chunked send');
                        return false;
                    }

                    const remainingSize = totalSize - offset;
                    const currentChunkSize = Math.min(chunkSize, remainingSize);

                    let chunk;
                    if (typeof data === 'string') {
                        chunk = data.slice(offset, offset + currentChunkSize);
                    } else if (data instanceof ArrayBuffer) {
                        chunk = data.slice(offset, offset + currentChunkSize);
                    } else if (data instanceof Uint8Array) {
                        chunk = data.slice(offset, offset + currentChunkSize);
                    } else {
                        console.error('[WebSocket] Unsupported data type for chunking');
                        return false;
                    }

                    server.send(chunk);
                    offset += currentChunkSize;
                    chunkCount++;
                }

                console.log(`[WebSocket] Successfully sent ${totalSize} bytes in ${chunkCount} chunks`);
                return true;
            } catch (e) {
                console.error('[WebSocket] Chunked send failed:', e.message);
                GLOBAL_ERROR_HANDLER.log(e, 'WebSocketChunkedSend');
                return false;
            }
        };

        // 🛡️ 创建 WebSocket 对
        const webSocketPair = ULTIMATE_SAFE.sync(() => new WebSocketPair(), 'WebSocketPair.create');
        if (!webSocketPair) {
            return new Response('WebSocket creation failed', { status: 500 });
        }

        const [clientSocket, serverSocket] = Object.values(webSocketPair);
        client = clientSocket;
        server = serverSocket;

        // 🛡️ 接受连接
        const acceptResult = ULTIMATE_SAFE.sync(() => server.accept(), 'server.accept');
        if (acceptResult === null) {
            return new Response('WebSocket accept failed', { status: 500 });
        }

        const earlyHeader = request.headers.get("sec-websocket-protocol") || "";
        let ingressMode = "transform";

        // 🛡️ 创建流
        if (ingressMode === "transform") {
            upstreamReadable = ULTIMATE_SAFE.sync(() => {
                const transformStream = new TransformStream();
                let holdWriter = null;

                const writerResult = ULTIMATE_SAFE.sync(() => transformStream.writable.getWriter(), 'getWriter');
                if (!writerResult) {
                    absoluteCleanup('Writer creation failed');
                    return transformStream.readable;
                }
                holdWriter = writerResult;

                // 处理 early header
                if (earlyHeader) {
                    ULTIMATE_SAFE.async(async () => {
                        const earlyData = decodeBase64Url(earlyHeader);
                        if (earlyData && holdWriter) {
                            await holdWriter.write(earlyData);
                        }
                    }, 'EarlyHeader');
                }

                // 🔧 改进的消息处理器 - 添加大小检查和错误处理
                const handleMessage = (e) => {
                    ULTIMATE_SAFE.sync(() => {
                        if (!holdWriter || isSessionClosed) {
                            return;
                        }

                        // 🔧 检查消息大小
                        const messageSize = getDataSize(e.data);

                        if (messageSize > WS_MESSAGE_LIMITS.MAX_SIZE) {
                            // 消息过大，记录错误并优雅关闭连接
                            const errorMsg = `WebSocket message too large: ${messageSize} bytes (limit: ${WS_MESSAGE_LIMITS.MAX_SIZE})`;
                            console.error('[WebSocket]', errorMsg);
                            GLOBAL_ERROR_HANDLER.log(new Error(errorMsg), 'WebSocketMessageTooLarge');

                            // 优雅关闭连接
                            if (server && server.readyState === WS_STATES.OPEN) {
                                server.close(1009, 'Message too large');
                            }
                            absoluteCleanup('Message size limit exceeded');
                            return;
                        }

                        // 🔧 记录大消息的接收
                        if (messageSize > WS_MESSAGE_LIMITS.CHUNK_SIZE) {
                            console.log(`[WebSocket] Received large message: ${messageSize} bytes`);
                        }

                        // 正常处理消息
                        holdWriter.write(e.data);
                    }, 'WebSocket.message');
                };

                const handleClose = (e) => {
                    ULTIMATE_SAFE.sync(() => {
                        if (holdWriter && !isSessionClosed) {
                            holdWriter.close();
                        }
                        absoluteCleanup('WebSocket closed');
                    }, 'WebSocket.close');
                };

                const handleError = (e) => {
                    ULTIMATE_SAFE.sync(() => {
                        if (holdWriter && !isSessionClosed) {
                            holdWriter.abort();
                        }
                        absoluteCleanup('WebSocket error');
                    }, 'WebSocket.error');
                };

                // 绑定事件
                ULTIMATE_SAFE.sync(() => {
                    server['onmessage'] = handleMessage;
                    server['onclose'] = handleClose;
                    server['onerror'] = handleError;
                }, 'EventBinding');

                return transformStream.readable;
            }, 'CreateTransformStream');
        }

        if (!upstreamReadable) {
            absoluteCleanup('Stream creation failed');
            return new Response('Stream creation failed', { status: 500 });
        }

        // 🛡️ 管道操作
        pipePromise = ULTIMATE_SAFE.promise(async () => {
            return upstreamReadable.pipeTo(
                new WritableStream({
                    async write(chunk) {
                        return ULTIMATE_SAFE.promise(async () => {
                            if (isSessionClosed) return;

                            if (tcpWriter) {
                                await tcpWriter.write(chunk);
                                return;
                            }

                            // 简单的协议解析
                            const header = await parseProtocolHeader(chunk, server, protocolMode);

                            // 🔧 简化的连接建立，直接使用createConnection
                            try {
                                tcpInterface = await createConnection(header, globalControllerConfig.connectMode, protocolMode);
                                await tcpInterface.opened;
                            } catch (connectError) {
                                console.warn('First connection failed, retrying with relay mode');
                                tcpInterface = await createConnection(header, globalControllerConfig.retryMode, protocolMode);
                                await tcpInterface.opened;
                            }

                            tcpWriter = ULTIMATE_SAFE.sync(() => tcpInterface.writable.getWriter(), 'getTcpWriter');
                            if (!tcpWriter) throw new Error('Failed to get TCP writer');

                            // 发送协议响应
                            if (protocolMode === globalControllerConfig.targetProtocolType0) {
                                const responseData = ULTIMATE_SAFE.sync(() => Uint8Array.of(header.version, 0), 'CreateResponse');
                                if (responseData) absoluteSend(responseData);
                            }

                            // 写入初始数据
                            if (header.rawClientData) {
                                await tcpWriter.write(header.rawClientData);
                            }

                            // � 安全的 TCP 到 WebSocket 转发 - 使用ULTIMATE_SAFE包装
                            const tcpForwardPromise = ULTIMATE_SAFE.promise(async () => {
                                tcpReader = tcpInterface.readable.getReader();
                                if (!tcpReader) return;

                                // 🔧 强化的高性能读取循环 - 确保所有错误都被正确捕获
                                while (!isSessionClosed && !forceStop) {
                                    try {
                                        // 🔧 确保 tcpReader.read() 的所有 Promise 拒绝都被捕获
                                        const result = await tcpReader.read();
                                        const { value, done } = result;

                                        if (done || !value) {
                                            console.log('[TCP.read] Stream ended normally');
                                            break;
                                        }

                                        // 🔧 检查要发送的数据大小
                                        const dataSize = getDataSize(value);
                                        if (dataSize > 0) {
                                            console.log(`[TCP.read] Sending ${dataSize} bytes to WebSocket`);
                                        }

                                        if (!absoluteSend(value)) {
                                            console.log('[TCP.read] Send failed, breaking loop');
                                            break;
                                        }
                                    } catch (readError) {
                                        // 🔧 记录所有读取错误的详细信息
                                        console.warn('[TCP.read] Read error occurred:', {
                                            message: readError.message,
                                            name: readError.name,
                                            isSessionClosed,
                                            forceStop
                                        });

                                        // 🔧 确保错误被记录到全局处理器
                                        GLOBAL_ERROR_HANDLER.log(readError, 'TCPRead');

                                        // 🔧 无论什么错误都应该触发清理并退出循环
                                        absoluteCleanup('TCP read error');
                                        break;
                                    }
                                }
                            }, 'TCPForward').finally(() => {
                                // 🔧 安全的清理逻辑
                                ULTIMATE_SAFE.sync(() => {
                                    if (tcpReader) {
                                        try {
                                            tcpReader.releaseLock();
                                        } catch (e) {
                                            GLOBAL_ERROR_HANDLER.log(e, 'TCPReader.releaseLock');
                                        }
                                    }
                                    if (!forceStop) {
                                        absoluteCleanup('TCP forward finished');
                                    }
                                }, 'TCPForward.cleanup');
                            });

                        }, 'WritableStream.write');
                    },
                    close() {
                        absoluteCleanup('WritableStream closed');
                    },
                    abort(reason) {
                        absoluteCleanup('WritableStream aborted');
                    }
                })
            );
        }, 'PipeOperation');

        // 🔧 简化的管道结果处理
        if (pipePromise && typeof pipePromise.catch === 'function') {
            pipePromise.catch((error) => {
                GLOBAL_ERROR_HANDLER.log(error, 'PipePromise');
                absoluteCleanup('Pipe failed');
            });
        }

        // 超时保护
        const timeoutId = ULTIMATE_SAFE.timeout(() => {
            if (!isSessionClosed) {
                absoluteCleanup('Session timeout');
            }
        }, 300000, 'SessionTimeout');

        // 清理超时
        if (pipePromise && typeof pipePromise.finally === 'function') {
            pipePromise.finally(() => {
                ULTIMATE_SAFE.sync(() => clearTimeout(timeoutId), 'clearTimeout');
            });
        }

        return new Response(null, { status: 101, webSocket: client });

    }, 'HandleSession', new Response('Session failed', { status: 500 }));
}

// 🔧 移除了多余的dial函数包装，直接使用createConnection

async function createConnection(header, mode, protocolMode) {
    const { addressType, addressRemote, portRemote } = header;
    const useTargetProtocol = protocolMode === globalControllerConfig.targetProtocolType0;

    switch (mode) {
        case 'relayip': {
            const needDirect =
                [1].includes(addressType) ||
                (useTargetProtocol && [3].includes(addressType)) ||
                (!useTargetProtocol && [4].includes(addressType));
            return needDirect
                ? connect({ hostname: addressRemote, port: portRemote })
                : connect({
                    hostname: globalSessionConfig.relay.ip,
                    port: globalSessionConfig.relay.port || portRemote,
                });
        }
        case 'relaysocks': {
            return socks5Connect(addressType, addressRemote, portRemote, useTargetProtocol);
        }
        case 'direct': {
            return connect({ hostname: addressRemote, port: portRemote });
        }
        default:
            return connect({ hostname: addressRemote, port: portRemote });
    }
}

function matchUuid(extractedID, uuidString) {
    return ULTIMATE_SAFE.sync(() => {
        if (!extractedID || !uuidString) return false;
        uuidString = uuidString.replaceAll('-', '');
        if (extractedID.length !== 16 || uuidString.length !== 32) return false;
        
        for (let index = 0; index < 16; index++) {
            const expected = parseInt(uuidString.substring(index * 2, index * 2 + 2), 16);
            if (isNaN(expected) || extractedID[index] !== expected) return false;
        }
        return true;
    }, 'matchUuid', false);
}

function parseAddress(bytes, view, decoder, offset, addressType, addressTypeMap) {
    return ULTIMATE_SAFE.sync(() => {
        let hostname = '';

        switch (addressType) {
            case addressTypeMap.IPv4: {
                if (offset + 4 > bytes.length) throw new Error('IPv4 data insufficient');
                hostname = `${bytes[offset]}.${bytes[offset + 1]}.${bytes[offset + 2]}.${bytes[offset + 3]}`;
                offset += 4;
                break;
            }
            case addressTypeMap.DOMAIN: {
                if (offset >= bytes.length) throw new Error('Domain length insufficient');
                const domainLen = bytes[offset++];
                if (offset + domainLen > bytes.length) throw new Error('Domain data insufficient');
                hostname = decoder.decode(bytes.subarray(offset, offset + domainLen));
                offset += domainLen;
                break;
            }
            case addressTypeMap.IPv6: {
                if (offset + 16 > bytes.length) throw new Error('IPv6 data insufficient');
                hostname = view.getUint16(offset).toString(16);
                for (let i = 1; i < 8; i++) {
                    hostname += ':' + view.getUint16(offset + i * 2).toString(16);
                }
                offset += 16;
                break;
            }
            case 49: {
                if (offset + 16 > bytes.length) throw new Error('IPv6 type 49 data insufficient');
                for (let i = 0; i < 8; i++) {
                    hostname += (i ? ':' : '') + view.getUint16(offset + i * 2).toString(16);
                }
                offset += 16;
                break;
            }
            default: {
                throw new Error(`Unsupported address type: ${addressType}`);
            }
        }

        return { hostname, offset };
    }, 'parseAddress', { hostname: '', offset: 0 });
}

async function parseProtocolHeader(buffer, wsInterface, protocolMode) {
    return ULTIMATE_SAFE.promise(async () => {
        if (!buffer || buffer.byteLength === 0) throw new Error('Invalid buffer');

        const bytes = new Uint8Array(buffer);
        const view = new DataView(buffer);
        const decoder = new TextDecoder();

        const addressTypeMap = protocolMode === globalControllerConfig.targetProtocolType0
            ? { IPv4: 1, DOMAIN: 2, IPv6: 3 }
            : { IPv4: 1, DOMAIN: 3, IPv6: 4 };

        if (protocolMode === globalControllerConfig.targetProtocolType0) {
            if (bytes.length < 17) throw new Error('Header too short for UUID');
            
            const extractedID = bytes.subarray(1, 17);
            if (!matchUuid(extractedID, globalSessionConfig.user.id)) {
                if (wsInterface && typeof wsInterface.close === 'function') {
                    ULTIMATE_SAFE.sync(() => wsInterface.close(1013, 'Invalid user'), 'wsClose.invalidUser');
                }
                throw new Error('Invalid user');
            }

            if (bytes.length < 19) throw new Error('Header too short for command');

            const version = bytes[0];
            const optionsLength = bytes[17];
            const commandIndex = 18 + optionsLength;
            
            if (commandIndex >= bytes.length) throw new Error('Header too short for command');
            
            const command = bytes[commandIndex];
            const portIndex = 18 + optionsLength + 1;
            
            if (portIndex + 2 > bytes.length) throw new Error('Header too short for port');
            
            const port = view.getUint16(portIndex);
            let offset = portIndex + 2;
            
            if (offset >= bytes.length) throw new Error('Header too short for address type');
            
            const addressType = bytes[offset++];

            const { hostname, offset: newOffset } = parseAddress(bytes, view, decoder, offset, addressType, addressTypeMap);
            const rawClientData = bytes.subarray(newOffset);

            return {
                addressType: addressType,
                addressRemote: hostname,
                portRemote: port,
                rawClientData,
                version
            };
        } else if (protocolMode === globalControllerConfig.targetProtocolType1) {
            const crLfIndex = 56;
            
            if (bytes.length < crLfIndex) throw new Error('Header too short for password');
            
            const extractedPassword = decoder.decode(bytes.subarray(0, crLfIndex));
            if (extractedPassword !== globalSessionConfig.user.sha224) {
                if (wsInterface && typeof wsInterface.close === 'function') {
                    ULTIMATE_SAFE.sync(() => wsInterface.close(1013, 'Invalid password'), 'wsClose.invalidPassword');
                }
                throw new Error('Invalid password');
            }

            let offset = crLfIndex + 2;
            
            if (offset + 2 > bytes.length) throw new Error('Header too short for command and address');
            
            const command = bytes[offset++];
            const addressType = bytes[offset++];

            const { hostname, offset: newOffset } = parseAddress(bytes, view, decoder, offset, addressType, addressTypeMap);
            
            if (newOffset + 2 > bytes.length) throw new Error('Header too short for port');
            
            const port = view.getUint16(newOffset);
            const rawClientData = bytes.subarray(newOffset + 4);

            return {
                addressType: addressType,
                addressRemote: hostname,
                portRemote: port,
                rawClientData
            };
        } else {
            throw new Error(`Unsupported protocol mode: ${protocolMode}`);
        }
    }, 'parseProtocolHeader');
}

function decodeBase64Url(encodedString) {
    return ULTIMATE_SAFE.sync(() => {
        if (!encodedString || typeof encodedString !== 'string') return null;
        const normalizedString = encodedString.replaceAll('-', '+').replaceAll('_', '/');
        const decoded = atob(normalizedString);
        return Uint8Array.from(decoded, (c) => c.charCodeAt(0)).buffer;
    }, 'decodeBase64Url', null);
}

async function socks5Connect(addressType, addressRemote, portRemote, targetProtocol) {
    return ULTIMATE_SAFE.promise(async () => {
        let socket = null;
        let reader = null;
        let writer = null;
        
        const cleanup = () => ULTIMATE_SAFE.sync(() => {
            if (reader) {
                ULTIMATE_SAFE.sync(() => {
                    try {
                        if (!reader.locked) {
                            reader.releaseLock();
                        }
                    } catch (e) {
                        console.warn('SOCKS5 reader releaseLock failed:', e.message);
                    }
                }, 'socks5.reader.release');
            }
            if (writer) {
                ULTIMATE_SAFE.sync(() => {
                    try {
                        if (!writer.locked) {
                            writer.releaseLock();
                        }
                    } catch (e) {
                        console.warn('SOCKS5 writer releaseLock failed:', e.message);
                    }
                }, 'socks5.writer.release');
            }
            if (socket) ULTIMATE_SAFE.async(() => socket.close(), 'socks5.socket.close');
        }, 'socks5.cleanup');

        try {
            const { username, password, hostname, port } = socks5AddressParser(globalSessionConfig.relay.socks);
            const encoder = new TextEncoder();
            
            socket = await connect({ hostname, port });
            reader = socket.readable.getReader();
            writer = socket.writable.getWriter();
            
            if (!reader || !writer) {
                cleanup();
                throw new Error('Failed to create SOCKS5 streams');
            }
            
            // SOCKS5 握手
            const socksGreeting = new Uint8Array([5, 2, 0, 2]);
            await writer.write(socksGreeting);
            
            const readResult = await reader.read();
            const res = readResult.value;
            if (!res || res.length < 2 || res[0] !== 0x05) {
                throw new Error('Invalid SOCKS5 response');
            }
            
            if (res[1] === 0xff) {
                throw new Error('SOCKS5 authentication rejected');
            }
            
            // 认证处理
            if (res[1] === 0x02) {
                if (!username || !password) throw new Error('SOCKS5 auth required');
                
                const authRequest = new Uint8Array([
                    1, username.length, ...encoder.encode(username),
                    password.length, ...encoder.encode(password)
                ]);
                await writer.write(authRequest);
                
                const authResult = await reader.read();
                const authRes = authResult.value;
                if (!authRes || authRes.length < 2 || authRes[0] !== 0x01 || authRes[1] !== 0x00) {
                    throw new Error('SOCKS5 authentication failed');
                }
            }
            
            // 构建目标地址
            let DSTADDR;
            const addressTypeMap = targetProtocol
                ? { IPv4: 1, DOMAIN: 2, IPv6: 3 }
                : { IPv4: 1, DOMAIN: 3, IPv6: 4 };
                
            switch (addressType) {
                case addressTypeMap.IPv4:
                    const ipParts = addressRemote.split('.');
                    if (ipParts.length !== 4) throw new Error('Invalid IPv4');
                    DSTADDR = new Uint8Array([1, ...ipParts.map(part => {
                        const num = Number(part);
                        if (isNaN(num) || num < 0 || num > 255) throw new Error('Invalid IPv4 part');
                        return num;
                    })]);
                    break;
                case addressTypeMap.DOMAIN:
                    if (!addressRemote || addressRemote.length === 0 || addressRemote.length > 255) {
                        throw new Error('Invalid domain');
                    }
                    DSTADDR = new Uint8Array([3, addressRemote.length, ...encoder.encode(addressRemote)]);
                    break;
                case addressTypeMap.IPv6:
                    DSTADDR = new Uint8Array([4, ...addressRemote.split(':').flatMap(x => 
                        x.padStart(4, '0').match(/.{2}/g).map(y => parseInt(y, 16)))]);
                    break;
                default:
                    throw new Error(`Unsupported address type: ${addressType}`);
            }
            
            // 发送连接请求
            const socksRequest = new Uint8Array([5, 1, 0, ...DSTADDR, portRemote >> 8, portRemote & 0xff]);
            await writer.write(socksRequest);
            
            const finalResult = await reader.read();
            const finalRes = finalResult.value;
            if (!finalRes || finalRes.length < 2 || finalRes[1] !== 0x00) {
                throw new Error(`SOCKS5 connection failed: ${finalRes ? finalRes[1] : 'no response'}`);
            }
            
            // 安全释放 reader 和 writer
            try {
                if (reader && !reader.locked) {
                    reader.releaseLock();
                }
            } catch (e) {
                console.warn('SOCKS5 final reader releaseLock failed:', e.message);
            }

            try {
                if (writer && !writer.locked) {
                    writer.releaseLock();
                }
            } catch (e) {
                console.warn('SOCKS5 final writer releaseLock failed:', e.message);
            }
            
            return socket;

        } catch (error) {
            cleanup();
            throw error;
        }
    }, 'socks5Connect');
}

function socks5AddressParser(address) {
    return ULTIMATE_SAFE.sync(() => {
        if (!address || typeof address !== 'string') {
            throw new Error('Invalid SOCKS address');
        }
        
        const [latter, former] = address.split("@").reverse();
        if (!latter) throw new Error('Invalid SOCKS format');
        
        const [hostname, port] = latter.split(":");
        if (!hostname) throw new Error('Missing hostname');
        
        let username, password;
        if (former) {
            const formers = former.split(":");
            if (formers.length !== 2) throw new Error('Invalid auth format');
            [username, password] = formers;
        }
        
        const portNum = Number(port);
        if (!port || isNaN(portNum) || portNum <= 0 || portNum > 65535) {
            throw new Error('Invalid port');
        }
        
        return { username, password, hostname, port: portNum };
    }, 'socks5AddressParser', { username: null, password: null, hostname: 'localhost', port: 1080 });
}

async function fetchRemoteData(url) {
    const randomVersion = Math.floor(Math.random() * (128 - 110 + 1)) + 110;
    const headers = new Headers({
        'User-Agent': `Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${randomVersion}.0.0.0 Safari/537.36`,
    });
    const response = await fetch(url, { headers });
    if (!response.ok) throw new Error('Failed to fetch');
    return response.text();
}

function encryptVar(str) {
    return str.split('').map(char => (char.charCodeAt(0) << 2).toString(16)).join('-_-');
}

function decryptVar(str) {
    return str.split('-_-').map(hex => String.fromCharCode(parseInt(hex, 16) >> 2)).join('');
}

const protocolTypes = {
    p0: decryptVar('1d8-_-1b0-_-194-_-1cc-_-1cc'),
    p1: decryptVar('1d0-_-1c8-_-1bc-_-1a8-_-184-_-1b8'),
};

function parseAddressLines(inputString) {
    const lines = inputString.trim().split('\n');
    const uniqueEntries = new Set(lines);
    const commentCounts = {};

    return Array.from(uniqueEntries).map(line => {
        let atValue, atType = null;
        let processedLine = line;

        const typeIndex = processedLine.indexOf('|');
        if (typeIndex > -1) {
            const typeValue = processedLine.substring(typeIndex + 1).trim().toLowerCase();
            atType = typeValue || null;
            processedLine = processedLine.slice(0, typeIndex).trim();
        }

        const atIndex = processedLine.indexOf('@');
        if (atIndex > -1) {
            atValue = processedLine.substring(atIndex + 1).trim();
            processedLine = processedLine.substring(0, atIndex).trim();
        }

        const [addressPort, comment] = processedLine.split('#');
        let processedComment = comment ? comment.trim() : 'Unknown';

        commentCounts[processedComment] = (commentCounts[processedComment] || 0) + 1;
        processedComment += `-${commentCounts[processedComment]}`;

        if (addressPort.startsWith('[')) {
            const closingBracketIndex = addressPort.indexOf(']');
            if (closingBracketIndex > -1) {
                const address = addressPort.substring(0, closingBracketIndex + 1);
                const port = addressPort.slice(closingBracketIndex + 2).trim() || null;
                return { address, port: port || null, comment: processedComment, atValue, atType };
            } else {
                // 处理格式错误的 IPv6 地址，作为普通地址处理
                const [address, port] = addressPort.trim().split(':');
                return { address: address.trim(), port: port ? port.trim() : null, comment: processedComment, atValue, atType };
            }
        } else {
            const [address, port] = addressPort.trim().split(':');
            return { address: address.trim(), port: port ? port.trim() : null, comment: processedComment, atValue, atType };
        }
    });
}

function generateConfigs(inputString, uuid, password, enableTls, domain) {
    const transportProtocol = 'ws';
    const fingerprint = 'chrome';

    function getPath(protocol, atValue, atType) {
        const basePath = protocol === protocolTypes.p0 ? '/?ed=2560' : '/trws?ed=2560';
        if (!atValue) return basePath;
        switch (atType?.toLowerCase()) {
            case 'socks': return `${basePath}&socks=${atValue}`;
            default: return `${basePath}&relayip=${atValue}`;
        }
    }

    const parsedAddresses = parseAddressLines(inputString);
    const configs = {
        protocolP0: [], protocolP1: [],
        customP0: { fullConfig: [], namesOnly: [] },
        customP1: { fullConfig: [], namesOnly: [] }
    };

    function generateProtocolString(protocol, credentials, address, port, comment, atValue, atType) {
        const securityType = enableTls ? 'tls' : 'none';
        const path = getPath(protocol, atValue, atType);
        const baseString = `${protocol}://${credentials}@${address}:${port}?security=${securityType}&sni=${domain}&fp=${fingerprint}&type=${transportProtocol}&host=${domain}&path=${encodeURIComponent(path)}`;
        const encryptionPart = protocol === protocolTypes.p0 ? '&encryption=none' : '';
        return `${baseString}${encryptionPart}#${encodeURIComponent(comment)}`;
    }

    function generateCustomConfig(protocol, address, port, comment, atValue, atType) {
        const isFirstProtocol = protocol === protocolTypes.p0;
        const nodeName = `${protocol}-${comment}`;
        const path = getPath(protocol, atValue, atType);

        return {
            name: nodeName, type: protocol, server: address,
            port: enableTls ? (port || 443) : (port || 80),
            [isFirstProtocol ? 'uuid' : 'password']: isFirstProtocol ? uuid : password,
            udp: true, tls: enableTls, network: "ws",
            [isFirstProtocol ? 'servername' : 'sni']: domain,
            ...(enableTls ? { "skip-cert-verify": false, "client-fingerprint": "chrome" } : {}),
            "ws-opts": { path: path, headers: { Host: domain } }
        };
    }

    parsedAddresses.forEach(({ address, port, comment, atValue, atType }) => {
        const actualPort = enableTls ? (port || 443) : (port || 80);
        const protocolP0 = generateProtocolString(protocolTypes.p0, uuid, address, actualPort, comment, atValue, atType);
        const protocolP1 = generateProtocolString(protocolTypes.p1, password, address, actualPort, comment, atValue, atType);
        configs.protocolP0.push(protocolP0.trim());
        configs.protocolP1.push(protocolP1.trim());
        const customP0 = generateCustomConfig(protocolTypes.p0, address, actualPort, comment, atValue, atType);
        const customP1 = generateCustomConfig(protocolTypes.p1, address, actualPort, comment, atValue, atType);
        configs.customP0.fullConfig.push('  - ' + JSON.stringify(customP0));
        configs.customP1.fullConfig.push('  - ' + JSON.stringify(customP1));
        configs.customP0.namesOnly.push(`      - "${customP0.name}"`);
        configs.customP1.namesOnly.push(`      - "${customP1.name}"`);
    });

    return {
        protocolP0: configs.protocolP0.join('\n'),
        protocolP1: configs.protocolP1.join('\n'),
        customP0: { fullConfig: configs.customP0.fullConfig.join('\n'), namesOnly: configs.customP0.namesOnly.join('\n') },
        customP1: { fullConfig: configs.customP1.fullConfig.join('\n'), namesOnly: configs.customP1.namesOnly.join('\n') }
    };
}

function getCustomConfig(inputString, uuid, pass, tls, domain, configType, inputTemplate) {
    const configs = generateConfigs(inputString, uuid, pass, tls, domain);
    let proxiesConfig, namesOnly;

    switch (configType.toLowerCase()) {
        case 'p0': proxiesConfig = configs.customP0.fullConfig; namesOnly = configs.customP0.namesOnly; break;
        case 'p1': proxiesConfig = configs.customP1.fullConfig; namesOnly = configs.customP1.namesOnly; break;
        case 'both': proxiesConfig = configs.customP0.fullConfig + '\n' + configs.customP1.fullConfig; namesOnly = configs.customP0.namesOnly + '\n' + configs.customP1.namesOnly; break;
        default: throw new Error(`Invalid configType: ${configType}`);
    }

    return inputTemplate.replace('${proxiesConfig}', proxiesConfig);
}